@extends('layouts.auth')

@section('title', 'Inscription')

@section('content')
<div class="auth-card">
    <div class="auth-header">
        <div class="logo-container">
            <i class="fas fa-building"></i>
        </div>
        <h1 class="auth-title">Rejoignez-nous !</h1>
        <p class="auth-subtitle">Créez votre compte pour réserver vos espaces</p>
    </div>

    <div class="auth-body">
        <!-- Flash Messages -->
        @if(session('error'))
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
            </div>
        @endif

        <form method="POST" action="{{ route('register') }}">
            @csrf

            <!-- Name -->
            <div class="form-group">
                <label for="name" class="form-label">
                    <i class="fas fa-user"></i>Nom complet
                </label>
                <input type="text"
                       class="form-control @error('name') is-invalid @enderror"
                       id="name"
                       name="name"
                       value="{{ old('name') }}"
                       placeholder="Votre nom complet"
                       required
                       autocomplete="name"
                       autofocus>
                @error('name')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Email -->
            <div class="form-group">
                <label for="email" class="form-label">
                    <i class="fas fa-envelope"></i>Adresse email
                </label>
                <input type="email"
                       class="form-control @error('email') is-invalid @enderror"
                       id="email"
                       name="email"
                       value="{{ old('email') }}"
                       placeholder="<EMAIL>"
                       required
                       autocomplete="email">
                @error('email')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <!-- Password -->
            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock"></i>Mot de passe
                </label>
                <input type="password"
                       class="form-control @error('password') is-invalid @enderror"
                       id="password"
                       name="password"
                       placeholder="••••••••"
                       required
                       autocomplete="new-password">
                @error('password')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>Minimum 8 caractères recommandés
                </small>
            </div>

            <!-- Confirm Password -->
            <div class="form-group">
                <label for="password_confirmation" class="form-label">
                    <i class="fas fa-shield-alt"></i>Confirmer le mot de passe
                </label>
                <input type="password"
                       class="form-control"
                       id="password_confirmation"
                       name="password_confirmation"
                       placeholder="••••••••"
                       required
                       autocomplete="new-password">
            </div>

            <!-- Terms and Conditions -->
            <div class="form-check mb-4">
                <input type="checkbox" class="form-check-input" id="terms" required>
                <label class="form-check-label" for="terms">
                    J'accepte les <a href="#">conditions d'utilisation</a>
                    et la <a href="#">politique de confidentialité</a>
                </label>
            </div>

            <!-- Submit Button -->
            <div class="d-grid mb-4">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-user-plus me-2"></i>Créer mon compte
                </button>
            </div>
        </form>

        <!-- QR Code Info -->
        <div class="alert alert-info mb-4">
            <div class="d-flex align-items-center">
                <i class="fas fa-qrcode fa-2x me-3"></i>
                <div>
                    <strong>🎉 Bonus QR Code</strong><br>
                    <small>Un QR code unique sera généré pour une connexion rapide !</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="auth-footer">
        <p>
            Déjà un compte ?
            <a href="{{ route('login') }}">Se connecter</a>
        </p>
    </div>
</div>
@endsection
