@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('admin.locals.index') }}">Locaux</a></li>
            <li class="breadcrumb-item active">Créer un nouveau local</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-plus-circle me-2"></i>Créer un nouveau local
                    </h1>
                    <p class="text-muted">Ajoutez un nouveau local à votre plateforme</p>
                </div>
                <div>
                    <a href="{{ route('admin.locals.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Informations du local
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.locals.store') }}" enctype="multipart/form-data">
                        @csrf

                        <!-- Nom -->
                        <div class="mb-3">
                            <label for="name" class="form-label">Nom du local <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name" value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="4" required>{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Type -->
                        <div class="mb-3">
                            <label for="type" class="form-label">Type de local <span class="text-danger">*</span></label>
                            <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                                <option value="">Sélectionnez un type</option>
                                <option value="sport" {{ old('type') == 'sport' ? 'selected' : '' }}>Terrain de sport</option>
                                <option value="conference" {{ old('type') == 'conference' ? 'selected' : '' }}>Salle de conférence</option>
                                <option value="fête" {{ old('type') == 'fête' ? 'selected' : '' }}>Salle de fête</option>
                            </select>
                            @error('type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Localisation -->
                        <div class="mb-3">
                            <label for="location" class="form-label">Localisation <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('location') is-invalid @enderror"
                                   id="location" name="location" value="{{ old('location') }}" required>
                            @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Prix -->
                        <div class="mb-3">
                            <label for="price" class="form-label">Prix par heure (MAD) <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control @error('price') is-invalid @enderror"
                                       id="price" name="price" value="{{ old('price') }}" min="0" step="0.01" required>
                                <span class="input-group-text">MAD/h</span>
                            </div>
                            @error('price')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Capacité -->
                        <div class="mb-3">
                            <label for="capacity" class="form-label">Capacité maximale</label>
                            <input type="number" class="form-control @error('capacity') is-invalid @enderror"
                                   id="capacity" name="capacity" value="{{ old('capacity') }}" min="1">
                            @error('capacity')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Équipements -->
                        <div class="mb-3">
                            <label for="equipment" class="form-label">Équipements disponibles</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="wifi" id="equipment_wifi" {{ in_array('wifi', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_wifi">
                                            <i class="fas fa-wifi me-2"></i>WiFi
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="projecteur" id="equipment_projecteur" {{ in_array('projecteur', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_projecteur">
                                            <i class="fas fa-video me-2"></i>Projecteur
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="climatisation" id="equipment_climatisation" {{ in_array('climatisation', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_climatisation">
                                            <i class="fas fa-snowflake me-2"></i>Climatisation
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="parking" id="equipment_parking" {{ in_array('parking', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_parking">
                                            <i class="fas fa-car me-2"></i>Parking
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="son" id="equipment_son" {{ in_array('son', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_son">
                                            <i class="fas fa-volume-up me-2"></i>Système audio
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="eclairage" id="equipment_eclairage" {{ in_array('eclairage', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_eclairage">
                                            <i class="fas fa-lightbulb me-2"></i>Éclairage professionnel
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="vestiaires" id="equipment_vestiaires" {{ in_array('vestiaires', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_vestiaires">
                                            <i class="fas fa-tshirt me-2"></i>Vestiaires
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="equipment[]" value="cuisine" id="equipment_cuisine" {{ in_array('cuisine', old('equipment', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="equipment_cuisine">
                                            <i class="fas fa-utensils me-2"></i>Cuisine équipée
                                        </label>
                                    </div>
                                </div>
                            </div>
                            @error('equipment')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Sélectionnez tous les équipements disponibles dans ce local</small>
                        </div>

                        <!-- Image -->
                        <div class="mb-3">
                            <label for="image" class="form-label">Image du local</label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror"
                                   id="image" name="image" accept="image/*">
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Formats acceptés: JPG, PNG, GIF. Taille max: 2MB</small>
                        </div>

                        <!-- Disponibilité -->
                        <div class="mb-3">
                            <label class="form-label">Disponibilité</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_available" name="is_available"
                                       value="1" {{ old('is_available', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_available">
                                    Local disponible pour les réservations
                                </label>
                            </div>
                        </div>

                        <!-- Boutons -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.locals.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Créer le local
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Aide -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Aide
                    </h6>
                </div>
                <div class="card-body">
                    <h6>Conseils pour créer un local :</h6>
                    <ul class="small">
                        <li>Utilisez un nom descriptif et accrocheur</li>
                        <li>Décrivez clairement les caractéristiques</li>
                        <li>Ajoutez une image de qualité</li>
                        <li>Indiquez tous les équipements disponibles</li>
                        <li>Fixez un prix compétitif</li>
                    </ul>
                </div>
            </div>

            <!-- Aperçu du type -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-eye me-2"></i>Types de locaux
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <i class="fas fa-futbol text-success me-2"></i>
                        <strong>Sport :</strong> Terrains, gymnases, courts
                    </div>
                    <div class="mb-2">
                        <i class="fas fa-presentation-screen text-primary me-2"></i>
                        <strong>Conférence :</strong> Salles de réunion, amphithéâtres
                    </div>
                    <div>
                        <i class="fas fa-glass-cheers text-warning me-2"></i>
                        <strong>Fête :</strong> Salles de réception, espaces événementiels
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Aperçu de l'image
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // Créer un aperçu si nécessaire
            console.log('Image sélectionnée:', file.name);
        };
        reader.readAsDataURL(file);
    }
});

// Validation du formulaire
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const description = document.getElementById('description').value.trim();
    const type = document.getElementById('type').value;
    const location = document.getElementById('location').value.trim();
    const price = document.getElementById('price').value;

    if (!name || !description || !type || !location || !price) {
        e.preventDefault();
        alert('Veuillez remplir tous les champs obligatoires.');
        return false;
    }

    if (parseFloat(price) <= 0) {
        e.preventDefault();
        alert('Le prix doit être supérieur à 0.');
        return false;
    }
});
</script>
@endsection
