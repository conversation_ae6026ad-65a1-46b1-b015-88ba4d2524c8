@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-file-invoice me-2"></i>Facture #{{ $invoice->id }}
                    </h1>
                    <p class="text-muted">Détails de votre facture</p>
                </div>
                <div>
                    @if($invoice->isPaid())
                        <span class="badge bg-success fs-6">
                            <i class="fas fa-check-circle me-1"></i>Payée
                        </span>
                    @else
                        <span class="badge bg-danger fs-6">
                            <i class="fas fa-exclamation-circle me-1"></i>Non payée
                        </span>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Invoice Details -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <!-- Invoice Header -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h2 class="text-primary">LocaSpace</h2>
                            <p class="mb-0">
                                Plateforme de réservation de locaux<br>
                                Casablanca, Maroc<br>
                                Email: <EMAIL>
                            </p>
                        </div>
                        <div class="col-md-6 text-end">
                            <h4>FACTURE</h4>
                            <p class="mb-0">
                                <strong>Numéro :</strong> #{{ $invoice->id }}<br>
                                <strong>Date :</strong> {{ $invoice->created_at->format('d/m/Y') }}<br>
                                <strong>Échéance :</strong> {{ $invoice->created_at->addDays(30)->format('d/m/Y') }}
                            </p>
                        </div>
                    </div>

                    <hr>

                    <!-- Client Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Facturé à :</h5>
                            <div class="bg-light p-3 rounded">
                                <strong>{{ $invoice->reservation->user->name }}</strong><br>
                                {{ $invoice->reservation->user->email }}<br>
                                Client depuis : {{ $invoice->reservation->user->created_at->format('d/m/Y') }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>Réservation :</h5>
                            <div class="bg-light p-3 rounded">
                                <strong>{{ $invoice->reservation->local->name }}</strong><br>
                                {{ $invoice->reservation->local->location }}<br>
                                <small class="text-muted">Réservation #{{ $invoice->reservation->id }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Items -->
                    <div class="table-responsive mb-4">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Description</th>
                                    <th class="text-center">Date</th>
                                    <th class="text-center">Durée</th>
                                    <th class="text-end">Prix unitaire</th>
                                    <th class="text-end">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <strong>{{ $invoice->reservation->local->name }}</strong><br>
                                        <small class="text-muted">
                                            {{ ucfirst($invoice->reservation->local->type) }} -
                                            {{ $invoice->reservation->local->location }}
                                        </small><br>
                                        <small class="text-muted">
                                            {{ $invoice->reservation->start_time }} - {{ $invoice->reservation->end_time }}
                                        </small>
                                    </td>
                                    <td class="text-center">
                                        {{ $invoice->reservation->date->format('d/m/Y') }}
                                    </td>
                                    <td class="text-center">
                                        {{ $invoice->reservation->getDurationInHours() }}h
                                    </td>
                                    <td class="text-end">
                                        {{ abs($invoice->reservation->local->price) }} MAD
                                    </td>
                                    <td class="text-end">
                                        <strong>{{ abs($invoice->reservation->calculateAmount()) }} MAD</strong>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Invoice Totals -->
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Payment Information -->
                            <h6>Informations de paiement :</h6>
                            <div class="bg-light p-3 rounded">
                                @if($invoice->isPaid())
                                    <span class="text-success">
                                        <i class="fas fa-check-circle me-2"></i>
                                        Paiement reçu le {{ $invoice->updated_at->format('d/m/Y à H:i') }}
                                    </span>
                                @else
                                    <span class="text-danger">
                                        <i class="fas fa-exclamation-circle me-2"></i>
                                        En attente de paiement
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td>Sous-total :</td>
                                    <td class="text-end">{{ abs($invoice->reservation->calculateAmount()) }} MAD</td>
                                </tr>
                                <tr>
                                    <td>TVA (20%) :</td>
                                    <td class="text-end">{{ number_format(abs($invoice->reservation->calculateAmount() * 0.2), 2) }} MAD</td>
                                </tr>
                                <tr class="table-primary">
                                    <td><strong>Total TTC :</strong></td>
                                    <td class="text-end"><strong>{{ abs($invoice->amount) }} MAD</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="mt-4">
                        <h6>Conditions générales :</h6>
                        <small class="text-muted">
                            • Paiement exigible à réception de facture<br>
                            • Annulation possible jusqu'à 24h avant la réservation<br>
                            • En cas de retard de paiement, des pénalités peuvent s'appliquer<br>
                            • Pour toute question, contactez-nous à <EMAIL>
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if($invoice->isPaid())
                            <a href="{{ route('invoices.download', $invoice) }}" class="btn btn-success">
                                <i class="fas fa-download me-2"></i>Télécharger PDF
                            </a>
                            <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>Imprimer
                            </button>
                        @else
                            <button type="button" class="btn btn-warning w-100 mb-2" id="payInvoiceButton"
                                    data-invoice-id="{{ $invoice->id }}"
                                    data-amount="{{ abs($invoice->amount) }}">
                                <i class="fas fa-credit-card me-2"></i>Payer maintenant
                            </button>

                            <!-- Option pour marquer comme payé (admin) -->
                            @if(auth()->user()->isAdmin())
                            <button type="button" class="btn btn-success w-100 mb-2" onclick="markAsPaid({{ $invoice->id }})">
                                <i class="fas fa-check me-2"></i>Marquer comme payé
                            </button>
                            @endif
                            <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                                <i class="fas fa-print me-2"></i>Imprimer
                            </button>
                        @endif

                        <a href="{{ route('reservations.show', $invoice->reservation) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-eye me-2"></i>Voir la réservation
                        </a>
                    </div>
                </div>
            </div>

            <!-- Invoice Summary -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Résumé
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">{{ $invoice->amount }}MAD</h4>
                            <small class="text-muted">Montant total</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info">{{ abs($invoice->reservation->getDurationInHours()) }}h</h4>
                            <small class="text-muted">Durée</small>
                        </div>
                    </div>

                    <hr>

                    <div class="small">
                        <strong>Local :</strong> {{ $invoice->reservation->local->name }}<br>
                        <strong>Date :</strong> {{ $invoice->reservation->date->format('d/m/Y') }}<br>
                        <strong>Heure :</strong> {{ $invoice->reservation->start_time }} - {{ $invoice->reservation->end_time }}<br>
                        <strong>Statut :</strong>
                        @if($invoice->isPaid())
                            <span class="text-success">Payée</span>
                        @else
                            <span class="text-danger">Non payée</span>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Support -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-headset me-2"></i>Support
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text small">
                        Une question concernant cette facture ? Notre équipe est disponible pour vous aider.
                    </p>
                    <div class="d-grid">
                        <button type="button" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-envelope me-2"></i>Contacter le support
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
@media print {
    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .btn, .card-header, nav, .col-lg-4 {
        display: none !important;
    }

    .col-lg-8 {
        width: 100% !important;
    }
}
</style>
@endpush

@push('scripts')
<script src="https://js.stripe.com/v3/"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const payButton = document.getElementById('payInvoiceButton');

    if (payButton) {
        payButton.addEventListener('click', function() {
            const invoiceId = this.dataset.invoiceId;
            const amount = this.dataset.amount;

            // Afficher le loading
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Traitement...';
            this.disabled = true;

            // Créer le Payment Intent
            fetch('/api/stripe/create-payment-intent', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    invoice_id: invoiceId,
                    amount: amount
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Réponse API:', data);

                if (data.success) {
                    // Initialiser Stripe
                    const stripe = Stripe('{{ config("services.stripe.key") }}');

                    // Rediriger vers Stripe Checkout
                    return stripe.redirectToCheckout({
                        sessionId: data.session_id
                    });
                } else {
                    throw new Error(data.error || 'Erreur lors de la création du paiement');
                }
            })
            .then(result => {
                if (result && result.error) {
                    throw new Error(result.error.message);
                }
            })
            .catch(error => {
                console.error('Erreur de paiement:', error);

                // Restaurer le bouton
                payButton.innerHTML = '<i class="fas fa-credit-card me-2"></i>Payer maintenant';
                payButton.disabled = false;

                // Afficher l'erreur
                alert('Erreur lors du paiement: ' + error.message);
            });
        });
    }
});

// Fonction pour marquer comme payé (admin)
function markAsPaid(invoiceId) {
    if (confirm('Êtes-vous sûr de vouloir marquer cette facture comme payée ?')) {
        fetch(`/api/invoices/${invoiceId}/mark-paid`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la mise à jour');
        });
    }
}
</script>
@endpush
@endsection
