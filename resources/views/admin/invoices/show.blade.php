@extends('layouts.app')

@section('title', 'Détails de la Facture')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Administration
                    </h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ route('admin.dashboard') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a href="{{ route('admin.locals.index') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-building me-2"></i>Locaux
                    </a>
<<<<<<< HEAD
                    <a href="{{ route('admin.reservations.index') }}" class="list-group-item list-group-item-action">
=======
                    <a href="{{ route('admin.reservations') }}" class="list-group-item list-group-item-action">
>>>>>>> 8eb877e69cbbb9272e0949a8e48f42d023e48cf7
                        <i class="fas fa-calendar me-2"></i>Réservations
                    </a>
                    <a href="{{ route('admin.invoices.index') }}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-file-invoice me-2"></i>Factures
                    </a>
                    <a href="{{ route('admin.users.index') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2"></i>Utilisateurs
                    </a>
                    <a href="{{ route('admin.reports') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i>Rapports
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 main-content">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Facture #{{ $invoice->invoice_number }}</h1>
                    <p class="text-muted">Détails de la facture</p>
                </div>
                <div>
                    <a href="{{ route('admin.invoices.index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Retour
                    </a>
                    <a href="{{ route('invoices.download', $invoice) }}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-download me-2"></i>Télécharger PDF
                    </a>
                    @if($invoice->payment_status !== 'réglé')
                        <button type="button" class="btn btn-success" onclick="markAsPaid({{ $invoice->id }})">
                            <i class="fas fa-check me-2"></i>Marquer comme payée
                        </button>
                    @endif
                </div>
            </div>

            <!-- Invoice Details -->
            <div class="row">
                <div class="col-lg-8">
                    <!-- Invoice Info -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Informations de la Facture</h5>
                            @if($invoice->payment_status === 'réglé')
                                <span class="badge bg-success fs-6">Payée</span>
                            @elseif($invoice->payment_status === 'en_attente')
                                <span class="badge bg-warning fs-6">En Attente</span>
                            @else
                                <span class="badge bg-danger fs-6">Annulée</span>
                            @endif
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-3">Informations Client</h6>
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="avatar-lg bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                            {{ substr($invoice->reservation->user->name, 0, 2) }}
                                        </div>
                                        <div>
                                            <h6 class="mb-1">{{ $invoice->reservation->user->name }}</h6>
                                            <p class="text-muted mb-0">{{ $invoice->reservation->user->email }}</p>
                                            @if($invoice->reservation->user->phone)
                                                <p class="text-muted mb-0">{{ $invoice->reservation->user->phone }}</p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-3">Détails de la Facture</h6>
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="text-muted">Numéro :</td>
                                            <td class="fw-semibold">#{{ $invoice->invoice_number }}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">Date de création :</td>
                                            <td>{{ $invoice->created_at ? $invoice->created_at->format('d/m/Y à H:i') : 'Date inconnue' }}</td>
                                        </tr>
                                        @if($invoice->paid_at)
                                        <tr>
                                            <td class="text-muted">Date de paiement :</td>
                                            <td>{{ $invoice->paid_at->format('d/m/Y à H:i') }}</td>
                                        </tr>
                                        @endif
                                        <tr>
                                            <td class="text-muted">Montant total :</td>
                                            <td class="fw-bold text-success fs-5">{{ abs($invoice->amount) }} MAD</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reservation Details -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-white">
                            <h5 class="mb-0">Détails de la Réservation</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-3">Local Réservé</h6>
                                    <div class="d-flex align-items-start">
                                        @if($invoice->reservation->local->image)
                                            <img src="{{ asset('storage/' . $invoice->reservation->local->image) }}"
                                                 alt="{{ $invoice->reservation->local->name }}"
                                                 class="rounded me-3"
                                                 style="width: 80px; height: 80px; object-fit: cover;">
                                        @else
                                            <div class="bg-primary text-white rounded d-flex align-items-center justify-content-center me-3"
                                                 style="width: 80px; height: 80px;">
                                                <i class="fas fa-building fa-2x"></i>
                                            </div>
                                        @endif
                                        <div>
                                            <h6 class="mb-1">{{ $invoice->reservation->local->name }}</h6>
                                            <p class="text-muted mb-1">{{ $invoice->reservation->local->location }}</p>
                                            <span class="badge bg-info">{{ ucfirst($invoice->reservation->local->type) }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-3">Période de Réservation</h6>
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="text-muted">Date de début :</td>
                                            <td>{{ $invoice->reservation->date_debut ? $invoice->reservation->date_debut->format('d/m/Y à H:i') : 'Date non définie' }}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">Date de fin :</td>
                                            <td>{{ $invoice->reservation->date_fin ? $invoice->reservation->date_fin->format('d/m/Y à H:i') : 'Date non définie' }}</td>
                                        </tr>
                                        <tr>
                                            <td class="text-muted">Durée :</td>
<<<<<<< HEAD
                                            <td>
                                                @if($invoice->reservation->date_debut && $invoice->reservation->date_fin)
                                                    {{ $invoice->reservation->date_debut->diffInHours($invoice->reservation->date_fin) }} heures
                                                @else
                                                    Non calculable
                                                @endif
                                            </td>
=======
                                            <td>{{ abs($invoice->reservation->getDurationInHours()) }} heure(s)</td>
>>>>>>> 8eb877e69cbbb9272e0949a8e48f42d023e48cf7
                                        </tr>
                                        <tr>
                                            <td class="text-muted">Statut :</td>
                                            <td>
                                                @if($invoice->reservation->status === 'confirmé')
                                                    <span class="badge bg-success">Confirmée</span>
                                                @elseif($invoice->reservation->status === 'en_attente')
                                                    <span class="badge bg-warning">En Attente</span>
                                                @else
                                                    <span class="badge bg-danger">Annulée</span>
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    @if($invoice->notes)
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0">Notes</h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">{{ $invoice->notes }}</p>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Sidebar Actions -->
                <div class="col-lg-4">
                    <!-- Quick Actions -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>Actions Rapides
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('invoices.download', $invoice) }}" class="btn btn-outline-primary">
                                    <i class="fas fa-download me-2"></i>Télécharger PDF
                                </a>
                                @if($invoice->payment_status !== 'réglé')
                                    <button type="button" class="btn btn-outline-success" onclick="markAsPaid({{ $invoice->id }})">
                                        <i class="fas fa-check me-2"></i>Marquer comme payée
                                    </button>
                                @endif
                                <a href="{{ route('admin.invoices.edit', $invoice) }}" class="btn btn-outline-warning">
                                    <i class="fas fa-edit me-2"></i>Modifier
                                </a>
                                <button type="button" class="btn btn-outline-danger" onclick="deleteInvoice({{ $invoice->id }})">
                                    <i class="fas fa-trash me-2"></i>Supprimer
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Payment History -->
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">
                                <i class="fas fa-history me-2"></i>Historique
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">Facture créée</h6>
                                        <small class="text-muted">{{ $invoice->created_at ? $invoice->created_at->format('d/m/Y à H:i') : 'Date inconnue' }}</small>
                                    </div>
                                </div>
                                @if($invoice->paid_at)
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">Paiement reçu</h6>
                                        <small class="text-muted">{{ $invoice->paid_at->format('d/m/Y à H:i') }}</small>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function markAsPaid(invoiceId) {
    if (confirm('Êtes-vous sûr de vouloir marquer cette facture comme payée ?')) {
        fetch(`/api/invoices/${invoiceId}/mark-paid`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de la mise à jour du statut');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de la mise à jour du statut');
        });
    }
}

function deleteInvoice(invoiceId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette facture ? Cette action est irréversible.')) {
        fetch(`/admin/invoices/${invoiceId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '{{ route("admin.invoices.index") }}';
            } else {
                alert('Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de la suppression');
        });
    }
}
</script>

<style>
.avatar-lg {
    width: 60px;
    height: 60px;
    font-size: 1.25rem;
}

.sidebar {
    min-height: calc(100vh - 76px);
    background-color: #f8f9fa;
}

.main-content {
    min-height: calc(100vh - 76px);
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 20px;
    width: 2px;
    height: calc(100% + 10px);
    background-color: #dee2e6;
}

.timeline-marker {
    position: absolute;
    left: -26px;
    top: 4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content h6 {
    font-size: 0.875rem;
}
</style>
@endpush
@endsection
