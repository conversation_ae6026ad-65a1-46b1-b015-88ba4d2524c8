@extends('layouts.app')

@section('title', 'Créer une Facture')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">0
                        <i class="fas fa-cogs me-2"></i>Administration
                    </h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ route('admin.dashboard') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a href="{{ route('admin.locals.index') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-building me-2"></i>Locaux
                    </a>
                    <a href="{{ route('admin.reservations.index') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-calendar me-2"></i>Réservations
                    </a>
                    <a href="{{ route('admin.invoices.index') }}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-file-invoice me-2"></i>Factures
                    </a>
<<<<<<< HEAD
                    <a href="{{ route('admin.users.index') }}" class="list-group-item list-group-item-action">
=======
                    <a href="{{ route('admin.users') }}" class="list-group-item list-group-item-action">
>>>>>>> 8eb877e69cbbb9272e0949a8e48f42d023e48cf7
                        <i class="fas fa-users me-2"></i>Utilisateurs
                    </a>
                    <a href="{{ route('admin.reports') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i>Rapports
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 main-content">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Créer une Facture</h1>
                    <p class="text-muted">Créez une nouvelle facture manuellement</p>
                </div>
                <div>
                    <a href="{{ route('admin.invoices.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour
                    </a>
                </div>
            </div>

            <!-- Form -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0">Informations de la Facture</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('admin.invoices.store') }}">
                                @csrf

                                <!-- Réservation -->
                                <div class="mb-3">
                                    <label for="reservation_id" class="form-label">
                                        <i class="fas fa-calendar me-2"></i>Réservation
                                    </label>
                                    <select class="form-select @error('reservation_id') is-invalid @enderror"
                                            id="reservation_id" name="reservation_id" required>
                                        <option value="">Sélectionnez une réservation</option>
                                        @foreach($reservations as $reservation)
                                            <option value="{{ $reservation->id }}"
                                                    data-amount="{{ $reservation->total_amount }}"
                                                    {{ old('reservation_id') == $reservation->id ? 'selected' : '' }}>
                                                #{{ $reservation->id }} - {{ $reservation->local->name }}
                                                ({{ $reservation->user->name }}) -
                                                {{ $reservation->date_debut->format('d/m/Y') }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('reservation_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Montant -->
                                <div class="mb-3">
                                    <label for="amount" class="form-label">
                                        <i class="fas fa-coins me-2"></i>Montant Total (MAD)
                                    </label>
                                    <input type="number"
                                           class="form-control @error('amount') is-invalid @enderror"
                                           id="amount"
                                           name="amount"
                                           step="0.01"
                                           min="0"
                                           value="{{ old('amount') }}"
                                           required>
                                    @error('amount')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Statut de paiement -->
                                <div class="mb-3">
                                    <label for="payment_status" class="form-label">
                                        <i class="fas fa-credit-card me-2"></i>Statut de Paiement
                                    </label>
                                    <select class="form-select @error('payment_status') is-invalid @enderror"
                                            id="payment_status" name="payment_status" required>
                                        <option value="en_attente" {{ old('payment_status') === 'en_attente' ? 'selected' : '' }}>
                                            En Attente
                                        </option>
                                        <option value="réglé" {{ old('payment_status') === 'réglé' ? 'selected' : '' }}>
                                            Payée
                                        </option>
                                        <option value="annulé" {{ old('payment_status') === 'annulé' ? 'selected' : '' }}>
                                            Annulée
                                        </option>
                                    </select>
                                    @error('payment_status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Date de paiement (si payée) -->
                                <div class="mb-3" id="paid_at_field" style="display: none;">
                                    <label for="paid_at" class="form-label">
                                        <i class="fas fa-calendar-check me-2"></i>Date de Paiement
                                    </label>
                                    <input type="datetime-local"
                                           class="form-control @error('paid_at') is-invalid @enderror"
                                           id="paid_at"
                                           name="paid_at"
                                           value="{{ old('paid_at') }}">
                                    @error('paid_at')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Notes -->
                                <div class="mb-3">
                                    <label for="notes" class="form-label">
                                        <i class="fas fa-sticky-note me-2"></i>Notes (optionnel)
                                    </label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror"
                                              id="notes"
                                              name="notes"
                                              rows="3"
                                              placeholder="Notes additionnelles sur cette facture...">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Boutons -->
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('admin.invoices.index') }}" class="btn btn-outline-secondary">
                                        Annuler
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Créer la Facture
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Info -->
                <div class="col-lg-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Informations
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-2"></i>Conseils :</h6>
                                <ul class="list-unstyled mb-0 small">
                                    <li>• Sélectionnez une réservation pour auto-remplir le montant</li>
                                    <li>• Le numéro de facture sera généré automatiquement</li>
                                    <li>• Si la facture est marquée comme payée, indiquez la date de paiement</li>
                                    <li>• Les notes sont visibles sur la facture PDF</li>
                                </ul>
                            </div>

                            <div class="mt-3">
                                <h6>Statuts disponibles :</h6>
                                <div class="d-flex flex-column gap-2">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-warning me-2">En Attente</span>
                                        <small class="text-muted">Facture créée, en attente de paiement</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-success me-2">Payée</span>
                                        <small class="text-muted">Paiement reçu et confirmé</small>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-danger me-2">Annulée</span>
                                        <small class="text-muted">Facture annulée ou refusée</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Réservation sélectionnée -->
                    <div class="card border-0 shadow-sm mt-3" id="reservation_info" style="display: none;">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">
                                <i class="fas fa-calendar me-2"></i>Détails de la Réservation
                            </h6>
                        </div>
                        <div class="card-body" id="reservation_details">
                            <!-- Sera rempli par JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const reservationSelect = document.getElementById('reservation_id');
    const totalAmountInput = document.getElementById('amount');
    const paymentStatusSelect = document.getElementById('payment_status');
    const paidAtField = document.getElementById('paid_at_field');
    const paidAtInput = document.getElementById('paid_at');
    const reservationInfo = document.getElementById('reservation_info');
    const reservationDetails = document.getElementById('reservation_details');

    // Gestion du changement de réservation
    reservationSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];

        if (selectedOption.value) {
            const amount = selectedOption.getAttribute('data-amount');
            totalAmountInput.value = amount;

            // Afficher les détails de la réservation
            const text = selectedOption.text;
            reservationDetails.innerHTML = `
                <div class="small">
                    <strong>Réservation :</strong> ${text}<br>
                    <strong>Montant :</strong> ${amount} MAD
                </div>
            `;
            reservationInfo.style.display = 'block';
        } else {
            totalAmountInput.value = '';
            reservationInfo.style.display = 'none';
        }
    });

    // Gestion du statut de paiement
    paymentStatusSelect.addEventListener('change', function() {
        if (this.value === 'réglé') {
            paidAtField.style.display = 'block';
            paidAtInput.required = true;
            // Définir la date actuelle par défaut
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            paidAtInput.value = localDateTime;
        } else {
            paidAtField.style.display = 'none';
            paidAtInput.required = false;
            paidAtInput.value = '';
        }
    });

    // Initialiser l'affichage du champ de date de paiement
    if (paymentStatusSelect.value === 'réglé') {
        paidAtField.style.display = 'block';
        paidAtInput.required = true;
    }
});
</script>

<style>
.sidebar {
    min-height: calc(100vh - 76px);
    background-color: #f8f9fa;
}

.main-content {
    min-height: calc(100vh - 76px);
}
</style>
@endpush
@endsection
