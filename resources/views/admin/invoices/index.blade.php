@extends('layouts.app')

@section('title', 'Gestion des Factures')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="card border-0 shadow-sm" style="background-color: #3b3bf7 !important;">
                <div class="card-header text-blue-400" >
                    <h6 class="mb-0">
                        <i class="fas fa-cogs me-2 text-blue-400"></i>Administration
                    </h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ route('admin.dashboard') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a href="{{ route('admin.locals.index') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-building me-2"></i>Locaux
                    </a>
<<<<<<< HEAD
                    <a href="{{ route('admin.reservations.index') }}" class="list-group-item list-group-item-action">
=======
                    <a href="{{ route('admin.reservations') }}" class="list-group-item list-group-item-action">
>>>>>>> 8eb877e69cbbb9272e0949a8e48f42d023e48cf7
                        <i class="fas fa-calendar me-2"></i>Réservations
                    </a>
                    <a href="{{ route('admin.invoices.index') }}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-file-invoice me-2"></i>Factures
                    </a>
<<<<<<< HEAD
                    <a href="{{ route('admin.users.index') }}" class="list-group-item list-group-item-action">
=======
                    <a href="{{ route('admin.users') }}" class="list-group-item list-group-item-action">
>>>>>>> 8eb877e69cbbb9272e0949a8e48f42d023e48cf7
                        <i class="fas fa-users me-2"></i>Utilisateurs
                    </a>
                    <a href="{{ route('admin.reports') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-chart-bar me-2"></i>Rapports
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 main-content">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Gestion des Factures</h1>
                    <p class="text-muted">Gérez toutes les factures du système</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary me-2" onclick="exportInvoices()">
                        <i class="fas fa-download me-2"></i>Exporter
                    </button>
                    <a href="{{ route('admin.invoices.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Nouvelle Facture
                    </a>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="text-primary mb-2">
                                <i class="fas fa-file-invoice fa-2x"></i>
                            </div>
                            <h4 class="mb-1">{{ $invoices->total() }}</h4>
                            <small class="text-muted">Total Factures</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="text-success mb-2">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                            <h4 class="mb-1">{{ $invoices->where('payment_status', 'réglé')->count() }}</h4>
                            <small class="text-muted">Payées</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="text-warning mb-2">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                            <h4 class="mb-1">{{ $invoices->where('payment_status', 'en_attente')->count() }}</h4>
                            <small class="text-muted">En Attente</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <div class="text-info mb-2">
                                <i class="fas fa-coins fa-2x"></i>
                            </div>
                            <h4 class="mb-1">{{ number_format($totalRevenue, 0) }} MAD</h4>
                            <small class="text-muted">Revenus Total</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.invoices.index') }}" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Statut</label>
                            <select name="status" class="form-select">
                                <option value="">Tous les statuts</option>
                                <option value="en_attente" {{ request('status') === 'en_attente' ? 'selected' : '' }}>En Attente</option>
                                <option value="réglé" {{ request('status') === 'réglé' ? 'selected' : '' }}>Payée</option>
                                <option value="annulé" {{ request('status') === 'annulé' ? 'selected' : '' }}>Annulée</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date de</label>
                            <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date à</label>
                            <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Recherche</label>
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" placeholder="Numéro, client..." value="{{ request('search') }}">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Invoices Table -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Liste des Factures</h5>
                </div>
                <div class="card-body p-0">
                    @if($invoices->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Numéro</th>
                                        <th>Client</th>
                                        <th>Réservation</th>
                                        <th>Montant</th>
                                        <th>Statut</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($invoices as $invoice)
                                    <tr>
                                        <td>
                                            <strong class="text-primary">#{{ $invoice->invoice_number }}</strong>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    {{ substr($invoice->reservation->user->name, 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="fw-semibold">{{ $invoice->reservation->user->name }}</div>
                                                    <small class="text-muted">{{ $invoice->reservation->user->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <div class="fw-semibold">{{ $invoice->reservation->local->name }}</div>
                                                <small class="text-muted">
                                                    {{ $invoice->reservation->date_debut ? $invoice->reservation->date_debut->format('d/m/Y') : 'Date non définie' }}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">{{ abs($invoice->amount) }} MAD</span>
                                        </td>
                                        <td>
                                            @if($invoice->payment_status === 'réglé')
                                                <span class="badge bg-success">Payée</span>
                                            @elseif($invoice->payment_status === 'en_attente')
                                                <span class="badge bg-warning">En Attente</span>
                                            @else
                                                <span class="badge bg-danger">Annulée</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div>{{ $invoice->created_at ? $invoice->created_at->format('d/m/Y') : 'Date inconnue' }}</div>
                                            <small class="text-muted">{{ $invoice->created_at ? $invoice->created_at->format('H:i') : '' }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-sm btn-outline-primary" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if($invoice->payment_status !== 'réglé')
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="markAsPaid({{ $invoice->id }})" title="Marquer comme payée">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                @endif
                                                <a href="{{ route('invoices.download', $invoice) }}" class="btn btn-sm btn-outline-info" title="Télécharger PDF">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteInvoice({{ $invoice->id }})" title="Supprimer">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucune facture trouvée</h5>
                            <p class="text-muted">Aucune facture ne correspond à vos critères de recherche.</p>
                        </div>
                    @endif
                </div>

                @if($invoices->hasPages())
                <div class="card-footer bg-white">
                    <div class="d-flex justify-content-center">
                        {{ $invoices->appends(request()->query())->links() }}
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function markAsPaid(invoiceId) {
    if (confirm('Êtes-vous sûr de vouloir marquer cette facture comme payée ?')) {
        fetch(`/api/invoices/${invoiceId}/mark-paid`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de la mise à jour du statut');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de la mise à jour du statut');
        });
    }
}

function deleteInvoice(invoiceId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette facture ? Cette action est irréversible.')) {
        fetch(`/admin/invoices/${invoiceId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de la suppression');
        });
    }
}

function exportInvoices() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.location.href = `{{ route('admin.invoices.index') }}?${params.toString()}`;
}
</script>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
}

.sidebar {
    min-height: calc(100vh - 76px);
    background-color: #f8f9fa;
}

.main-content {
    min-height: calc(100vh - 76px);
}
</style>
@endpush
@endsection
