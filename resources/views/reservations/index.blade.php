@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>Mes réservations
                    </h1>
                    <p class="text-muted"><PERSON><PERSON><PERSON> toutes vos réservations</p>
                </div>
                <div>
                    <a href="{{ route('locals.index') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Nouvelle réservation
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('reservations.index') }}">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="status" class="form-label">Statut</label>
                                <select name="status" id="status" class="form-select">
                                    <option value="">Tous les statuts</option>
                                    <option value="confirmée" {{ request('status') === 'confirmée' ? 'selected' : '' }}>Confirmées</option>
                                    <option value="en attente" {{ request('status') === 'en attente' ? 'selected' : '' }}>En attente</option>
                                    <option value="annulée" {{ request('status') === 'annulée' ? 'selected' : '' }}>Annulées</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">Date de début</label>
                                <input type="date" name="date_from" id="date_from" class="form-control" 
                                       value="{{ request('date_from') }}">
                            </div>
                            <div class="col-md-3">
                                <label for="date_to" class="form-label">Date de fin</label>
                                <input type="date" name="date_to" id="date_to" class="form-control" 
                                       value="{{ request('date_to') }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-filter me-1"></i>Filtrer
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Reservations List -->
    <div class="row">
        <div class="col-12">
            @if($reservations->count() > 0)
                <div class="card">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Local</th>
                                        <th>Date</th>
                                        <th>Heure</th>
                                        <th>Durée</th>
                                        <th>Statut</th>
                                        <th>Montant</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($reservations as $reservation)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    @if($reservation->local->type === 'sport')
                                                        <i class="fas fa-futbol text-success fa-lg"></i>
                                                    @elseif($reservation->local->type === 'conference')
                                                        <i class="fas fa-presentation-screen text-primary fa-lg"></i>
                                                    @else
                                                        <i class="fas fa-glass-cheers text-warning fa-lg"></i>
                                                    @endif
                                                </div>
                                                <div>
                                                    <strong>{{ $reservation->local->name }}</strong><br>
                                                    <small class="text-muted">{{ $reservation->local->location }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <strong>{{ $reservation->date->format('d/m/Y') }}</strong><br>
                                            <small class="text-muted">{{ $reservation->date->format('l') }}</small>
                                        </td>
                                        <td>
                                            {{ $reservation->start_time }} - {{ $reservation->end_time }}
                                        </td>
                                        <td>
                                            {{ abs($reservation->getDurationInHours()) }}h
                                        </td>
                                        <td>
                                            @if($reservation->status === 'confirmée')
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>Confirmée
                                                </span>
                                            @elseif($reservation->status === 'en attente')
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>En attente
                                                </span>
                                            @else
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i>Annulée
                                                </span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($reservation->invoice)
                                                <strong>{{ $reservation->invoice->amount }}MAD</strong><br>
                                                @if($reservation->invoice->isPaid())
                                                    <small class="text-success">
                                                        <i class="fas fa-check-circle me-1"></i>Payé
                                                    </small>
                                                @else
                                                    <small class="text-danger">
                                                        <i class="fas fa-exclamation-circle me-1"></i>Non payé
                                                    </small>
                                                @endif
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('reservations.show', $reservation) }}" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   title="Voir les détails">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                
                                                @if($reservation->invoice && $reservation->invoice->isPaid())
                                                    <a href="{{ route('invoices.download', $reservation->invoice) }}" 
                                                       class="btn btn-sm btn-outline-success" 
                                                       title="Télécharger la facture">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                @endif

                                                @if(in_array($reservation->status, ['en attente', 'confirmée']))
                                                    <form method="POST" action="{{ route('reservations.cancel', $reservation) }}" 
                                                          style="display: inline;">
                                                        @csrf
                                                        @method('PATCH')
                                                        <button type="submit" 
                                                                class="btn btn-sm btn-outline-danger" 
                                                                title="Annuler la réservation"
                                                                onclick="return confirm('Êtes-vous sûr de vouloir annuler cette réservation ?')">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                @if($reservations->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $reservations->appends(request()->query())->links() }}
                </div>
                @endif
            @else
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h4>Aucune réservation trouvée</h4>
                    <p class="text-muted">
                        @if(request()->hasAny(['status', 'date_from', 'date_to']))
                            Aucune réservation ne correspond à vos critères de recherche.
                        @else
                            Vous n'avez pas encore effectué de réservation.
                        @endif
                    </p>
                    <div class="mt-4">
                        @if(request()->hasAny(['status', 'date_from', 'date_to']))
                            <a href="{{ route('reservations.index') }}" class="btn btn-outline-primary me-2">
                                <i class="fas fa-refresh me-2"></i>Réinitialiser les filtres
                            </a>
                        @endif
                        <a href="{{ route('locals.index') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Faire une réservation
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
