<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Local;
use App\Models\Reservation;
use App\Models\Invoice;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    /**
     * Show the admin dashboard.
     */
    public function dashboard()
    {
        $stats = [
            'total_users' => User::count(),
            'total_locals' => Local::count(),
            'active_locals' => Local::active()->count(),
            'total_reservations' => Reservation::count(),
            'confirmed_reservations' => Reservation::confirmed()->count(),
            'pending_reservations' => Reservation::pending()->count(),
            'total_revenue' => Invoice::paid()->sum('amount'),
            'pending_payments' => Invoice::unpaid()->sum('amount'),
        ];

        // Recent reservations
        $recentReservations = Reservation::with(['user', 'local'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Monthly revenue chart data
        $monthlyRevenue = Invoice::paid()
            ->select(
                DB::raw('YEAR(created_at) as year'),
                DB::raw('MONTH(created_at) as month'),
                DB::raw('SUM(amount) as total')
            )
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->limit(12)
            ->get();

        return view('admin.dashboard', compact('stats', 'recentReservations', 'monthlyRevenue'));
    }

    /**
     * Show all reservations.
     */
    public function reservations(Request $request)
    {
        $query = Reservation::with(['user', 'local', 'invoice']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('date', '<=', $request->date_to);
        }

        // Filter by local
        if ($request->filled('local_id')) {
            $query->where('local_id', $request->local_id);
        }

        $reservations = $query->orderBy('created_at', 'desc')->paginate(20);
        $locals = Local::all(['id', 'name']);

        return view('admin.reservations', compact('reservations', 'locals'));
    }

    /**
     * Show all users.
     */
    public function users(Request $request)
    {
        $query = User::withCount('reservations');

        // Search by name or email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.users', compact('users'));
    }

    /**
     * Show a specific user.
     */
    public function showUser(User $user)
    {
        $user->load(['reservations.local', 'reservations.invoice']);

        $userStats = [
            'total_reservations' => $user->reservations()->count(),
            'confirmed_reservations' => $user->reservations()->confirmed()->count(),
            'total_spent' => $user->reservations()
                ->join('invoices', 'reservations.id', '=', 'invoices.reservation_id')
                ->where('invoices.payment_status', 'réglé')
                ->sum('invoices.amount'),
        ];

        return view('admin.users.show', compact('user', 'userStats'));
    }

    /**
     * Show reports and analytics.
     */
    public function reports()
    {
        // Revenue by month
        $revenueByMonth = Invoice::paid()
            ->select(
                DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
                DB::raw('SUM(amount) as total')
            )
            ->groupBy('month')
            ->orderBy('month', 'desc')
            ->limit(12)
            ->get();

        // Reservations by local type
        $reservationsByType = Reservation::where('reservations.status', 'confirmée')
            ->join('locals', 'reservations.local_id', '=', 'locals.id')
            ->select('locals.type', DB::raw('COUNT(*) as count'))
            ->groupBy('locals.type')
            ->get();

        // Top locals by revenue
        $topLocalsByRevenue = Local::select('locals.id', 'locals.name', 'locals.location', 'locals.type', 'locals.price', DB::raw('SUM(invoices.amount) as total_revenue'))
            ->join('reservations', 'locals.id', '=', 'reservations.local_id')
            ->join('invoices', 'reservations.id', '=', 'invoices.reservation_id')
            ->where('invoices.payment_status', 'réglé')
            ->groupBy('locals.id', 'locals.name', 'locals.location', 'locals.type', 'locals.price')
            ->orderByRaw('SUM(invoices.amount) DESC')
            ->limit(10)
            ->get();

        return view('admin.reports', compact(
            'revenueByMonth',
            'reservationsByType',
            'topLocalsByRevenue'
        ));
    }

    /**
     * Show all invoices.
     */
    public function invoices(Request $request)
    {
        $query = Invoice::with(['reservation.user', 'reservation.local']);

        // Filter by payment status
        if ($request->filled('status')) {
            $query->where('payment_status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to);
        }

        // Search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('reservation.user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        $invoices = $query->orderBy('created_at', 'desc')->paginate(20);

<<<<<<< HEAD
        // Calculate total revenue
        $totalRevenue = Invoice::where('payment_status', 'réglé')->sum('amount');

        return view('admin.invoices.index', compact('invoices', 'totalRevenue'));
    }

    /**
     * Show the form for creating a new invoice.
     */
    public function createInvoice()
    {
        $reservations = Reservation::with(['user', 'local'])
            ->whereDoesntHave('invoice')
            ->where('status', 'confirmé')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('admin.invoices.create', compact('reservations'));
    }

    /**
     * Store a newly created invoice.
     */
    public function storeInvoice(Request $request)
    {
        $request->validate([
            'reservation_id' => 'required|exists:reservations,id',
            'amount' => 'required|numeric|min:0',
            'payment_status' => 'required|in:en_attente,réglé,annulé',
            'paid_at' => 'nullable|date',
            'notes' => 'nullable|string|max:1000',
        ]);

        $reservation = Reservation::findOrFail($request->reservation_id);

        // Check if invoice already exists for this reservation
        if ($reservation->invoice) {
            return back()->withErrors(['reservation_id' => 'Une facture existe déjà pour cette réservation.']);
        }

        $invoice = Invoice::create([
            'reservation_id' => $request->reservation_id,
            'invoice_number' => 'INV-' . date('Y') . '-' . str_pad(Invoice::count() + 1, 6, '0', STR_PAD_LEFT),
            'amount' => $request->amount,
            'payment_status' => $request->payment_status,
            'paid_at' => $request->payment_status === 'réglé' ? ($request->paid_at ?: now()) : null,
            'notes' => $request->notes,
        ]);

        return redirect()->route('admin.invoices.index')->with('success', 'Facture créée avec succès.');
    }

    /**
     * Display the specified invoice.
     */
    public function showInvoice(Invoice $invoice)
    {
        $invoice->load(['reservation.user', 'reservation.local']);
        return view('admin.invoices.show', compact('invoice'));
    }

    /**
     * Show the form for editing the specified invoice.
     */
    public function editInvoice(Invoice $invoice)
    {
        $reservations = Reservation::with(['user', 'local'])
            ->where(function($query) use ($invoice) {
                $query->whereDoesntHave('invoice')
                      ->orWhere('id', $invoice->reservation_id);
            })
            ->where('status', 'confirmé')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('admin.invoices.edit', compact('invoice', 'reservations'));
    }

    /**
     * Update the specified invoice.
     */
    public function updateInvoice(Request $request, Invoice $invoice)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0',
            'payment_status' => 'required|in:en_attente,réglé,annulé',
            'paid_at' => 'nullable|date',
            'notes' => 'nullable|string|max:1000',
        ]);

        $invoice->update([
            'amount' => $request->amount,
            'payment_status' => $request->payment_status,
            'paid_at' => $request->payment_status === 'réglé' ? ($request->paid_at ?: $invoice->paid_at ?: now()) : null,
            'notes' => $request->notes,
        ]);

        return redirect()->route('admin.invoices.show', $invoice)->with('success', 'Facture mise à jour avec succès.');
    }

    /**
     * Remove the specified invoice.
     */
    public function destroyInvoice(Invoice $invoice)
    {
        $invoice->delete();
        return response()->json(['success' => true, 'message' => 'Facture supprimée avec succès.']);
=======
        // Calculate total revenue from all paid invoices
        $totalRevenue = Invoice::paid()->sum('amount');

        return view('admin.invoices.index', compact('invoices', 'totalRevenue'));
>>>>>>> 8eb877e69cbbb9272e0949a8e48f42d023e48cf7
    }
}
