<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Stripe\Stripe;
use Stripe\PaymentIntent;
use Stripe\Customer;
use Stripe\Checkout\Session;
use App\Models\Reservation;
use App\Models\Invoice;
use App\Services\NotificationService;

class StripeController extends Controller
{
    public function __construct()
    {
        // Configurer Stripe avec la clé secrète
        Stripe::setApiKey(env('STRIPE_SECRET_KEY'));
    }

    /**
     * Créer une session Stripe Checkout pour une facture
     */
    public function createPaymentIntent(Request $request)
    {
        try {
            // Log pour debug
            \Log::info('Stripe createPaymentIntent called', $request->all());

            $request->validate([
                'invoice_id' => 'required|exists:invoices,id',
                'amount' => 'required|numeric'
            ]);

            $invoice = Invoice::findOrFail($request->invoice_id);
            $reservation = $invoice->reservation;
            $user = Auth::user();

            \Log::info('Invoice found', ['invoice_id' => $invoice->id, 'user_id' => $user->id]);

            // Vérifier que la réservation appartient à l'utilisateur
            if ($reservation->user_id !== $user->id) {
                return response()->json(['error' => 'Facture non autorisée'], 403);
            }

            // Vérifier que la facture n'est pas déjà payée
            if ($invoice->isPaid()) {
                return response()->json(['error' => 'Cette facture est déjà payée'], 400);
            }

            // Utiliser le montant de la facture et s'assurer qu'il est positif
            $amount = abs($invoice->amount);
            if ($amount < 1) {
                return response()->json(['error' => 'Le montant de la facture doit être supérieur à 0'], 400);
            }

            // Créer la session Stripe Checkout
            // Note: Stripe ne supporte pas MAD, on utilise EUR avec conversion approximative
            // 1 EUR ≈ 10.5 MAD (taux approximatif)
            $amountInEur = round($amount / 10.5, 2);

            $session = Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => 'eur',
                        'product_data' => [
                            'name' => "Réservation {$reservation->local->name}",
                            'description' => "Date: {$reservation->date->format('d/m/Y')} - Heure: {$reservation->start_time} à {$reservation->end_time} (Montant original: {$amount} MAD)",
                        ],
                        'unit_amount' => $amountInEur * 100, // Montant en centimes d'euro
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'payment',
                'success_url' => route('invoices.show', $invoice) . '?payment=success',
                'cancel_url' => route('invoices.show', $invoice) . '?payment=cancelled',
                'metadata' => [
                    'invoice_id' => $invoice->id,
                    'reservation_id' => $reservation->id,
                    'user_id' => $user->id,
                    'original_amount_mad' => $amount,
                ],
            ]);

            return response()->json([
                'success' => true,
                'session_id' => $session->id,
                'amount' => $amount,
                'amount_eur' => $amountInEur,
                'currency' => 'MAD',
                'currency_stripe' => 'EUR'
            ]);

        } catch (\Exception $e) {
            \Log::error('Stripe createPaymentIntent error', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json([
                'error' => 'Erreur lors de la création du paiement: ' . $e->getMessage(),
                'debug' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ], 500);
        }
    }

    /**
     * Confirmer le paiement
     */
    public function confirmPayment(Request $request)
    {
        $request->validate([
            'payment_intent_id' => 'required|string',
            'reservation_id' => 'required|exists:reservations,id'
        ]);

        try {
            // Récupérer le Payment Intent
            $paymentIntent = PaymentIntent::retrieve($request->payment_intent_id);

            if ($paymentIntent->status === 'succeeded') {
                // Mettre à jour la réservation
                $reservation = Reservation::findOrFail($request->reservation_id);
                $reservation->update([
                    'payment_status' => 'paid',
                    'payment_intent_id' => $paymentIntent->id,
                    'amount_paid' => $paymentIntent->amount / 100
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Paiement confirmé avec succès',
                    'reservation' => $reservation
                ]);
            } else {
                return response()->json([
                    'error' => 'Le paiement n\'a pas été confirmé'
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Erreur lors de la confirmation: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Webhook Stripe pour les événements de paiement
     */
    public function webhook(Request $request)
    {
        $payload = $request->getContent();
        $sig_header = $request->header('Stripe-Signature');
        $endpoint_secret = env('STRIPE_WEBHOOK_SECRET');

        try {
            $event = \Stripe\Webhook::constructEvent(
                $payload, $sig_header, $endpoint_secret
            );
        } catch(\UnexpectedValueException $e) {
            return response('Invalid payload', 400);
        } catch(\Stripe\Exception\SignatureVerificationException $e) {
            return response('Invalid signature', 400);
        }

        // Traiter l'événement
        switch ($event->type) {
            case 'checkout.session.completed':
                $session = $event->data->object;
                $this->handleSuccessfulCheckout($session);
                break;

            case 'payment_intent.succeeded':
                $paymentIntent = $event->data->object;
                $this->handleSuccessfulPayment($paymentIntent);
                break;

            case 'payment_intent.payment_failed':
                $paymentIntent = $event->data->object;
                $this->handleFailedPayment($paymentIntent);
                break;

            default:
                // Événement non géré
                break;
        }

        return response('Success', 200);
    }

    /**
     * Obtenir ou créer un customer Stripe
     */
    private function getOrCreateStripeCustomer($user)
    {
        if ($user->stripe_customer_id) {
            try {
                return Customer::retrieve($user->stripe_customer_id);
            } catch (\Exception $e) {
                // Customer n'existe plus, en créer un nouveau
            }
        }

        // Créer un nouveau customer
        $customer = Customer::create([
            'email' => $user->email,
            'name' => $user->name,
            'metadata' => [
                'user_id' => $user->id
            ]
        ]);

        // Sauvegarder l'ID customer
        $user->update(['stripe_customer_id' => $customer->id]);

        return $customer;
    }

    /**
     * Gérer une session Checkout réussie
     */
    private function handleSuccessfulCheckout($session)
    {
        $invoiceId = $session->metadata->invoice_id ?? null;

        if ($invoiceId) {
            $invoice = Invoice::find($invoiceId);
            if ($invoice) {
                $invoice->update([
                    'payment_status' => 'réglé',
                    'stripe_session_id' => $session->id,
                    'paid_at' => now()
                ]);

                // Mettre à jour la réservation
                $reservation = $invoice->reservation;
                if ($reservation) {
                    $reservation->update([
                        'status' => 'confirmée'
                    ]);

                    // Créer des notifications
                    $notificationService = new NotificationService();
                    $notificationService->notifyPaymentSuccessful($invoice);
                    $notificationService->notifyReservationConfirmed($reservation);
                }
            }
        }
    }

    /**
     * Gérer un paiement réussi
     */
    private function handleSuccessfulPayment($paymentIntent)
    {
        $reservationId = $paymentIntent->metadata->reservation_id ?? null;

        if ($reservationId) {
            $reservation = Reservation::find($reservationId);
            if ($reservation) {
                $reservation->update([
                    'payment_status' => 'paid',
                    'payment_intent_id' => $paymentIntent->id,
                    'amount_paid' => $paymentIntent->amount / 100
                ]);
            }
        }
    }

    /**
     * Gérer un paiement échoué
     */
    private function handleFailedPayment($paymentIntent)
    {
        $reservationId = $paymentIntent->metadata->reservation_id ?? null;

        if ($reservationId) {
            $reservation = Reservation::find($reservationId);
            if ($reservation) {
                $reservation->update([
                    'payment_status' => 'failed'
                ]);
            }
        }
    }
}
